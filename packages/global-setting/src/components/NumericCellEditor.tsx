/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useRef, useEffect, useState, forwardRef, memo, useImperativeHandle } from 'react';
import { type ICellEditorParams } from '@ag-grid-community/core';

export const NumericCellEditor = memo(
    forwardRef((props: ICellEditorParams, ref) => {
        const [value, setValue] = useState(parseInt(props.value));
        const refInput = useRef<HTMLInputElement>(null);

        useEffect(() => {
            refInput.current?.focus();
        }, []);

        useImperativeHandle(ref, () => {
            return {
                getValue() {
                    return value;
                },
                isCancelBeforeStart() {
                    return false;
                },
            };
        });

        return (
            <input
                type="number"
                ref={refInput}
                value={value}
                onChange={(event: any) => setValue(parseInt(event.target.value))}
                style={{ width: '100%', display: 'flex', height: '100%' }}
            />
        );
    })
);
