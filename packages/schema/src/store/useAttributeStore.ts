
import { create } from 'zustand';
import { fetch, schemaUrls } from "@tripudiotech/admin-api";
import { AttributeSchema } from 'ui-common';
import { PageInfo } from '@tripudiotech/admin-api/types/model/page-info.model';

type AttributeStore = {
    isLoading: boolean;
    getAttributes: (params?: { offset?: number; limit?: number; query?: string; reset?: boolean }) => Promise<any>;
    attributes: AttributeSchema[];
    pageInfo: PageInfo;
    resetAttributes: () => void;
}

const initialStates = {
    isLoading: false,
    attributes: [],
    pageInfo: null,
}

const useAttributeStore = create<AttributeStore>((set, get) => ({
    ...initialStates,
    resetAttributes: () => {
        set({ attributes: [], pageInfo: null });
    },
    getAttributes: async (params = {}) => {
        const { offset = 0, limit = 50, query, reset = false } = params;
        set({ isLoading: true });

        try {
            const queryParams: any = {
                offset,
                limit,
            };

            if (query) {
                queryParams.query = query;
            }

            const { data } = await fetch({
                ...schemaUrls.getListAttribute,
                qs: queryParams,
            });

            const currentAttributes = reset ? [] : get().attributes;
            const newAttributes = offset === 0 ? data.data : [...currentAttributes, ...data.data];

            set({
                attributes: newAttributes,
                pageInfo: data.pageInfo
            });
            return data.data;
        } catch (e) {
            console.error('Error fetching attributes:', e);
        } finally {
            set({ isLoading: false });
        }
    }
}))

export default useAttributeStore