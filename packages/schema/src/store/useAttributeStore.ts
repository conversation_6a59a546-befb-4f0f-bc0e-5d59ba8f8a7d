
import { create } from 'zustand';
import { fetch, schemaUrls } from "@tripudiotech/admin-api";
import { AttributeSchema } from 'ui-common';
import { PageInfo } from '@tripudiotech/admin-api/types/model/page-info.model';

type AttributeStore = {
    isLoading: boolean;
    getAttributes: () => Promise<any>;
    attributes: AttributeSchema[];
    pageInfo: PageInfo;
}

const initialStates = {
    isLoading: false,
    attributes: [],
    pageInfo: null,
}

const useAttributeStore = create<AttributeStore>((set, get) => ({
    ...initialStates,
    getAttributes: async () => {
        set({ isLoading: true });
        try {
            const { data } = await fetch({
                ...schemaUrls.getListAttribute,
                qs: {
                    limit: 50,
                },
            });
            set({
                attributes: data.data,
                pageInfo: data.pageInfo
            });
            return data.data;
        } catch (e) {
        } finally {
            set({ isLoading: false });
        }
    }
}))

export default useAttributeStore