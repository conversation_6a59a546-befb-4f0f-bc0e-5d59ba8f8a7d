/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import {
    AnimatedPage,
    Attribute<PERSON><PERSON><PERSON><PERSON><PERSON>,
    BooleanRenderer,
    styled,
    tableStyles,
    Ident<PERSON><PERSON><PERSON>er,
    RichtextCellRenderer,
    Box,
    Menu,
    MenuItem,
    Typography,
    Divider,
    TextField,
    InputAdornment,
    Button,
} from 'ui-style';
import { useToggle, ISchemaDetail, schemaHelper, AttributeSchema, ACTION } from 'ui-common';
import { useSchemaDetail, useUnitOfMeasure } from '@tripudiotech/admin-caching-store';
import { AgGridReact } from '@ag-grid-community/react';
import { RowDragEndEvent } from '@ag-grid-community/core';
import isEmpty from 'lodash/isEmpty';
import ChangeOrderAction from '../../components/actions/ChangeOrderAction';
import Identifier from './Identifier';
import AddExistingAttributeAction from '../../components/actions/AddExistingAttributeAction';
import AttributeDetail from './AttributeDetail';
import CreateNewAttributeAction from '../../components/actions/CreateNewAttributeAction';
import ChangeUniqueKeyAction from '../../components/actions/ChangeUniqueKeyAction';
import set from 'lodash/set';

const addSelectedAttributesToGroup = (groupName: string, gridApi: any) => {
    const selectedRows = gridApi.api.getSelectedRows();
    selectedRows.forEach((row) => set(row, 'group', groupName));
    gridApi.api.applyTransaction({ update: selectedRows });
};

const addSelectedAttributesToNewGroup = (groupName: string, gridApi: any) => {
    let selectedRows = gridApi.api.getSelectedRows();
    let newRows = [];
    gridApi.api.forEachNode((node) => {
        const isSelected = selectedRows.some((row) => row.id === node.id);
        newRows.push({
            ...node.data,
            group: isSelected ? groupName : node?.data?.group,
            index: isSelected ? 0 : node?.data?.index || 0 + 1,
        });
    });
    gridApi.api.applyTransaction({ update: newRows });
    gridApi.api.clearFocusedCell();
};

const AttributeContainer = styled('div')(({ theme }) => ({
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    '& .toolbar': {
        gap: '8px',
        display: 'flex',
        margin: '8px 16px',
        [theme.breakpoints.down('md')]: {
            '& .MuiButton-endIcon': {
                display: 'none',
            },
        },
    },
    '& .attributes': {
        height: '100%',
        width: '100%',
    },
}));

const Attribute = () => {
    const { schemaName } = useParams();
    const schemaDetail: ISchemaDetail = useSchemaDetail((state) => state.schema[schemaName]);
    const gridRef = useRef<AgGridReact>(null);
    const [changingOrder, changingOrderToggle] = useToggle(false);
    const navigate = useNavigate();
    const [searchParams, setSearchParams] = useSearchParams();
    const [getQuantityKind, getQuantityUnit] = useUnitOfMeasure((state) => [
        state.getQuantityKind,
        state.getQuantityUnit,
    ]);
    /**
     * Menu
     */
    const contextMenuPositionRef = useRef(null);
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);
    const [newGroupName, setNewGroupName] = useState(null);
    const handleCloseGroupMenu = useCallback(() => {
        setAnchorEl(null);
        setNewGroupName('');
    }, []);

    const handleNewGroup = useCallback(() => {
        addSelectedAttributesToNewGroup(newGroupName, gridRef.current);
        handleCloseGroupMenu();
    }, [newGroupName]);

    const addExistingGroupHandler = useCallback(
        (value) => {
            return () => {
                addSelectedAttributesToGroup(value, gridRef.current);
                handleCloseGroupMenu();
            };
        },
        [addSelectedAttributesToGroup]
    );

    const autoGroupColumnDef = useMemo(() => {
        return {
            headerName: 'Group',
            width: 220,
            minWidth: 200,
            rowDrag: true,
            flex: 1,
        };
    }, []);

    useEffect(() => {
        const contextMenuListener = (e) => {
            contextMenuPositionRef.current = e.target;
        };
        window.addEventListener('contextmenu', contextMenuListener);
        return () => {
            window.removeEventListener('contextmenu', contextMenuListener);
        };
    }, []);

    const [attributes, groupOrder] = useMemo(() => {
        if (schemaDetail) {
            return schemaHelper.getSchemaOrderedAttributesWithGroup(schemaDetail as any);
        }
        return [null, null];
    }, [schemaDetail]);

    const onIdentifierView = useCallback(
        (attribute: AttributeSchema) => {
            schemaHelper.openIdentifier(searchParams, setSearchParams, attribute);
        },
        [navigate]
    );

    const onAddIdentifier = useCallback(
        (attribute) => schemaHelper.openIdentifier(searchParams, setSearchParams, attribute, ACTION.CREATE),
        []
    );
    const onCellClicked = useCallback(
        (e) => {
            // Suppress click events on specific columns
            if (!['identifier', 'group', 'ag-Grid-AutoColumn'].includes(e.column.colId)) {
                e.node.setSelected(true);
                schemaHelper.openAttributeDetail(searchParams, setSearchParams, e.data);
            }
        },
        [searchParams, setSearchParams]
    );

    const getContextMenuItems = useCallback(() => {
        return [
            {
                name: 'Group attributes',
                action: () => {
                    setAnchorEl(contextMenuPositionRef.current);
                },
            },
            'expandAll',
            'contractAll',
            'separator',
            'copy',
            'export',
        ];
    }, []);

    const initialGroupOrderComparator = useCallback((params) => {
        const itemIndexA = params.nodeA.allLeafChildren?.[0]?.data?.index;
        const itemIndexB = params.nodeB.allLeafChildren?.[0]?.data?.index;
        return itemIndexA < itemIndexB ? 1 : -1;
    }, []);

    const onRowDragMove = useCallback((event: RowDragEndEvent) => {
        schemaHelper.handleRowGroupDragging(event, gridRef.current);
    }, []);

    useEffect(() => {
        if (changingOrder && gridRef.current) {
            gridRef.current?.api?.setSuppressRowDrag(false);
        } else {
            gridRef.current?.api?.setSuppressRowDrag(true);
        }
    }, [changingOrder]);

    const gridOptions: any = useMemo(() => {
        return {
            animateRows: true,
            autoGroupColumnDef,
            getContextMenuItems: getContextMenuItems,
            defaultColDef: {
                sortable: false,
                resizable: true,
                filter: true,
                floatingFilter: false,
                enableRowGroup: true,
                editable: false,
                cellStyle: () => ({
                    display: 'block',
                }),
                flex: 1,
            },
            getRowId: (params) => {
                return params.data.id;
            },
            rowModelType: 'clientSide',
            rowSelection: 'multiple',
            headerHeight: 32,
            rowHeight: 32,
            enableGroupEdit: true,
            columnDefs: [
                {
                    field: 'group',
                    headerName: 'Group',
                    filter: 'agTextColumnFilter',
                    rowGroup: true,
                    hide: true,
                },
                {
                    field: 'name',
                    headerName: 'Name',
                    filter: 'agTextColumnFilter',
                    cellRenderer: AttributeNameRenderer,
                    flex: 1,
                },
                {
                    field: 'displayName',
                    headerName: 'Display Name',
                    filter: 'agTextColumnFilter',
                    flex: 1,
                },
                {
                    field: 'type',
                    headerName: 'Type',
                    filter: 'agSetColumnFilter',
                    flex: 1,
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    filter: 'agTextColumnFilter',
                    cellRenderer: RichtextCellRenderer,
                },
                {
                    field: 'system',
                    headerName: 'System',
                    cellRenderer: BooleanRenderer,
                },
                {
                    field: 'nullable',
                    headerName: 'Can Be Empty',
                    cellRenderer: BooleanRenderer,
                },
                {
                    field: 'identifier',
                    headerName: 'Auto Generated',
                    filter: false,
                    cellRenderer: IdentifierRenderer,
                    cellRendererParams: {
                        onView: onIdentifierView,
                        onAdd: onAddIdentifier,
                    },
                    valueGetter: ({ data }) => {
                        const identifier = data?.identifier;
                        return identifier && !isEmpty(identifier)
                            ? `${identifier.prefix || ''}${identifier.nextAssignment || ''}${identifier.suffix || ''}`
                            : '';
                    },
                },
            ],
            suppressRowDrag: true,
            initialGroupOrderComparator,
            onRowDragMove,
            groupDefaultExpanded: 1,
        };
    }, [changingOrder, onIdentifierView, onAddIdentifier, initialGroupOrderComparator, onRowDragMove]);

    const redrawRows = useCallback(() => {
        gridRef.current?.api?.redrawRows();
        gridRef.current.columnApi?.autoSizeAllColumns();
    }, []);

    useEffect(() => {
        getQuantityKind();
        return () => {
            changingOrderToggle.close();
        };
    }, []);

    useEffect(() => {
        if (attributes) {
            attributes.forEach((attribute) => {
                const quantityKind = attribute.unitOfMeasure?.quantityKind;
                if (quantityKind) {
                    getQuantityUnit(quantityKind);
                }
            });
            redrawRows();
        }
    }, [attributes]);

    return (
        <AnimatedPage style={{ height: '100%' }}>
            <AttributeContainer sx={{ ...tableStyles }}>
                <div className="toolbar">
                    <CreateNewAttributeAction schemaDetail={schemaDetail} disabled={changingOrder} />
                    <AddExistingAttributeAction schemaDetail={schemaDetail} disabled={changingOrder} />
                    <ChangeUniqueKeyAction
                        attributes={attributes}
                        schemaDetail={schemaDetail}
                        disabled={changingOrder}
                    />
                    <ChangeOrderAction
                        schemaDetail={schemaDetail}
                        attributes={attributes}
                        changingOrder={changingOrder}
                        changingOrderToggle={changingOrderToggle}
                        gridRef={gridRef}
                    />
                </div>
                <div className="attributes ag-theme-alpine">
                    <AgGridReact ref={gridRef} {...gridOptions} rowData={attributes} onCellClicked={onCellClicked} />
                </div>
            </AttributeContainer>
            {attributes && (
                <>
                    <Identifier schemaDetail={schemaDetail} />
                    <AttributeDetail schemaDetail={schemaDetail} />
                </>
            )}
            <Menu
                id="basic-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleCloseGroupMenu}
                MenuListProps={{
                    'aria-labelledby': 'basic-button',
                }}
                sx={{
                    '& .MuiPaper-root': {
                        minWidth: '160px',
                    },
                }}
            >
                {groupOrder?.length > 0 && (
                    <>
                        <Box sx={{ margin: '8px', marginTop: '16px' }}>
                            <Typography variant="label2-med">Existing Group</Typography>
                            {groupOrder?.map((group) => (
                                <MenuItem key={group} onClick={addExistingGroupHandler(group)}>
                                    {group}
                                </MenuItem>
                            ))}
                        </Box>
                        <Divider />
                    </>
                )}
                <Box sx={{ margin: '8px', marginBottom: '16px' }}>
                    <Typography variant="label2-med">New Group</Typography>
                    <TextField
                        value={newGroupName}
                        size="small"
                        fullWidth
                        onChange={(e) => setNewGroupName(e.target.value)}
                        InputProps={{
                            endAdornment: (
                                <InputAdornment position="end">
                                    <Button
                                        disabled={!newGroupName}
                                        size="xs"
                                        variant="contained"
                                        onClick={handleNewGroup}
                                    >
                                        Save
                                    </Button>
                                </InputAdornment>
                            ),
                        }}
                    />
                </Box>
            </Menu>
        </AnimatedPage>
    );
};

export default Attribute;
