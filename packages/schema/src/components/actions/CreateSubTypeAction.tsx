/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { formH<PERSON><PERSON>, ISchemaDetail, SYSTEM_ENTITY_TYPE, useToggle } from 'ui-common';
import {
    Box,
    Button,
    FormikCheckBox,
    FormikTextField,
    MainTooltip,
    MenuItem,
    PlusIcon,
    RightTray,
    Typography,
} from 'ui-style';
import { expandEntityType, getCreateSubTypeTooltipMsg, getSchemaInfo } from '../../utils/helper';
import * as yup from 'yup';
import { DECOMPOSITION_MODEL, DECOMPOSITION_MODEL_OPTIONS, MASTER_MODEL_OPTIONS } from '../../constants';
import { COMPONENT_NAME } from '../../constants/component-name';
import { Formik, Form, Field } from 'formik';
import { createEntitySubType } from '../../actions';
import { useSchemaTree } from '@tripudiotech/admin-caching-store';
import useLocalStore from '../../store/useLocalStore';
import { useNavigate } from 'react-router-dom';

const CreateSubTypeAction = ({ detailSchema }: { detailSchema: ISchemaDetail }) => {
    const { getSchemaTree } = useSchemaTree();
    const [open, openToggle] = useToggle(false);
    const isMaster = detailSchema?.schemaName.includes(SYSTEM_ENTITY_TYPE.ENTITY_MASTER);
    const [withMaster, setWithMaster] = useState(false);
    const extendable = detailSchema?.entityType?.extendable;
    const setIsLoading = useLocalStore((state) => state.setIsLoading);
    const navigate = useNavigate();
    const formRef = useRef(null);

    const hasDecompositionModel = useMemo(
        () =>
            [DECOMPOSITION_MODEL.REV_TO_MASTER, DECOMPOSITION_MODEL.REV_TO_REV].includes(
                detailSchema?.decompositionModel?.type
            ),
        [detailSchema]
    );

    const initialValues = useMemo(
        () => ({
            name: '',
            description: '',
            isExtendable: false,
            isAbstract: false,
            isVisible: false,
            withMaster: MASTER_MODEL_OPTIONS[0].id,
            decompositionName: '',
            decompositionDescription: '',
            decompositionModel:
                detailSchema?.decompositionModel?.type !== DECOMPOSITION_MODEL.NONE
                    ? detailSchema?.decompositionModel?.type
                    : null,
        }),
        [detailSchema]
    );

    const validationSchema = useMemo(() => {
        const isBomRequired = withMaster && !hasDecompositionModel;
        return yup.object().shape({
            name: yup
                .string()
                .matches(new RegExp('^[a-zA-Z0-9_]+$', 'g'), 'Name must contain only letters and digits or underscores')
                .required(formHelper.buildRequiredMessage('Name'))
                .typeError(formHelper.buildRequiredMessage('Name')),
            displayName: yup
                .string()
                .typeError(formHelper.buildRequiredMessage('Display Name'))
                .required(formHelper.buildRequiredMessage('Display Name')),
            description: yup
                .string()
                .required(formHelper.buildRequiredMessage('Description'))
                .typeError(formHelper.buildRequiredMessage('Description')),
            decompositionModel: yup
                .string()
                .required(formHelper.buildRequiredMessage('Decomposition Model'))
                .typeError(formHelper.buildRequiredMessage('Decomposition Model')),
            isExtendable: yup.boolean().nullable().default(false),
            isAbstract: yup.boolean().nullable().default(false),
            isVisible: yup.boolean().nullable().default(false),
            decompositionName: isBomRequired
                ? yup
                      .string()
                      .required(formHelper.buildRequiredMessage('Decomposition Name'))
                      .typeError(formHelper.buildRequiredMessage('Decomposition Name'))
                : yup.string().nullable(),
            decompositionDisplayName: isBomRequired
                ? yup
                      .string()
                      .required(formHelper.buildRequiredMessage('Decomposition Display Name'))
                      .typeError(formHelper.buildRequiredMessage('Decomposition Display Name'))
                : yup.string().nullable(),
            decompositionDescription: isBomRequired
                ? yup
                      .string()
                      .required(formHelper.buildRequiredMessage('Decomposition Description'))
                      .typeError(formHelper.buildRequiredMessage('Decomposition Description'))
                : yup.string().nullable(),
        });
    }, [withMaster, hasDecompositionModel]);

    const handleSubmit = useCallback(
        async (values) => {
            const isBomRequired = !hasDecompositionModel && withMaster;
            setIsLoading(true);
            const entityType = {
                name: values.name,
                description: values.description,
                displayName: values.displayName,
                isExtendable: values.isExtendable,
                isAbstract: values.isAbstract,
                hasMaster: withMaster || hasDecompositionModel,
                isVisible: values.isVisible,
                decompositionModel: {
                    type: values.decompositionModel,
                    name: isBomRequired ? values.decompositionName : null,
                    displayName: isBomRequired ? values.decompositionDisplayName : null,
                    description: isBomRequired ? values.decompositionDescription : null,
                },
            };
            const parentType = getSchemaInfo(detailSchema, 'name');
            let hasError;
            const response = await createEntitySubType(parentType, entityType);
            hasError = response.error;

            // Expand schema tree to see new items
            expandEntityType(parentType);
            if (!hasError) {
                getSchemaTree(true);
                navigate(`../${entityType.name}/metadata`);
                openToggle.close();
            }
            setIsLoading(false);
        },
        [detailSchema, withMaster, hasDecompositionModel]
    );

    return (
        <>
            <MainTooltip
                title={getCreateSubTypeTooltipMsg(extendable, isMaster, detailSchema?.entityType?.displayName)}
            >
                <span>
                    <Button
                        disabled={isMaster || !extendable}
                        variant="contained-white"
                        size="xs"
                        endIcon={<PlusIcon />}
                        onClick={openToggle.open}
                    >
                        Create Sub Type
                    </Button>
                </span>
            </MainTooltip>
            <RightTray
                title={`Create sub type of ${getSchemaInfo(detailSchema, 'displayName')}`}
                componentName={COMPONENT_NAME.CREATE_SUB_TYPE}
                open={open}
                onClose={openToggle.close}
                onConfirm={() => formRef.current.submitForm()}
                confirmText="Create"
                disableCloseOnClickOutside
            >
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: '12px', margin: '16px' }}>
                    <Formik
                        enableReinitialize
                        initialValues={initialValues}
                        innerRef={formRef}
                        validationSchema={validationSchema}
                        onSubmit={(values, { setSubmitting }) => {
                            handleSubmit(values as any);
                            setSubmitting(false);
                        }}
                    >
                        {({ values, setFieldValue, ...rest }) => {
                            return (
                                <Form
                                    id="metadata-form"
                                    style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}
                                >
                                    <Typography
                                        variant="label2-med"
                                        sx={{
                                            color: (theme) => theme.palette.info.main,
                                        }}
                                    >
                                        General Information
                                    </Typography>
                                    <Field
                                        fullWidth
                                        component={FormikTextField}
                                        label="Name"
                                        name="name"
                                        variant="outlined"
                                    />
                                    <Field
                                        fullWidth
                                        component={FormikTextField}
                                        label="Display Name"
                                        name="displayName"
                                        variant="outlined"
                                    />
                                    <Field
                                        fullWidth
                                        component={FormikTextField}
                                        label="Description"
                                        name="description"
                                        variant="outlined"
                                    />
                                    <Field
                                        fullWidth
                                        component={FormikCheckBox}
                                        type="checkbox"
                                        name="isExtendable"
                                        Label={{ label: 'Extendable' }}
                                    />
                                    <Field
                                        fullWidth
                                        component={FormikCheckBox}
                                        type="checkbox"
                                        name="isAbstract"
                                        Label={{ label: 'Abstract' }}
                                    />
                                    <Field
                                        fullWidth
                                        component={FormikCheckBox}
                                        type="checkbox"
                                        name="isVisible"
                                        Label={{ label: 'Visible in UI' }}
                                    />
                                    <>
                                        <Typography
                                            variant="label2-med"
                                            sx={{
                                                color: (theme) => theme.palette.info.main,
                                            }}
                                        >
                                            Decomposition Model
                                        </Typography>
                                        <Field
                                            fullWidth
                                            select
                                            type="text"
                                            name="decompositionModel"
                                            label="Decomposition model"
                                            disabled={hasDecompositionModel}
                                            variant="outlined"
                                            component={FormikTextField}
                                            onChange={(e) => {
                                                setFieldValue('decompositionModel', e.target.value);
                                                setWithMaster(e.target.value !== DECOMPOSITION_MODEL.NONE);
                                            }}
                                        >
                                            {DECOMPOSITION_MODEL_OPTIONS.map((item) => (
                                                <MenuItem value={item.id} key={item.id}>
                                                    {item.value}
                                                </MenuItem>
                                            ))}
                                        </Field>
                                        {hasDecompositionModel && (
                                            <Typography
                                                sx={{
                                                    color: (theme) => theme.palette.glide.text.normal.inversePrimary,
                                                }}
                                                variant="label3-reg"
                                            >{`Decomposition model will be inherited from the parent entity type: ${detailSchema?.entityType.displayName}`}</Typography>
                                        )}
                                        {[DECOMPOSITION_MODEL.REV_TO_MASTER, DECOMPOSITION_MODEL.REV_TO_REV].includes(
                                            values.decompositionModel
                                        ) &&
                                            !hasDecompositionModel && (
                                                <>
                                                    <Field
                                                        fullWidth
                                                        component={FormikTextField}
                                                        label="Decomposition Name"
                                                        name="decompositionName"
                                                        variant="outlined"
                                                    />
                                                    <Field
                                                        fullWidth
                                                        component={FormikTextField}
                                                        label="Decomposition Display Name"
                                                        name="decompositionDisplayName"
                                                        variant="outlined"
                                                    />
                                                    <Field
                                                        fullWidth
                                                        component={FormikTextField}
                                                        label="Decomposition Description"
                                                        name="decompositionDescription"
                                                        variant="outlined"
                                                    />
                                                </>
                                            )}
                                    </>
                                </Form>
                            );
                        }}
                    </Formik>
                </Box>
            </RightTray>
        </>
    );
};

export default CreateSubTypeAction;
