/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React from 'react';
import { AttributeSchema, formatDateTime, ISchemaDetail } from 'ui-common';
import { AttributeSchemaDetail, Chip, Grid, SchemaField, SchemaFieldSection } from 'ui-style';
import get from 'lodash/get';
import { useUnitOfMeasure } from '@tripudiotech/admin-caching-store';

const AttributeInformation = ({
    attributeDetail,
    schemaDetail,
}: {
    attributeDetail: AttributeSchema;
    schemaDetail?: ISchemaDetail;
}) => {
    const [qdtUnitMapper, qdtKindMapper] = useUnitOfMeasure((state) => [state.qdtUnitMapper, state.qdtKindMapper]);
    return (
        <>
            <SchemaFieldSection>General Information</SchemaFieldSection>
            <Grid container spacing={2}>
                <Grid item xs={12}>
                    <SchemaField label="Name" value={get(attributeDetail, 'name')} />
                    <SchemaField label="Display Name" value={get(attributeDetail, 'displayName')} />
                    <SchemaField
                        label="Type"
                        value={
                            <Chip variant="status-info-outlined" size="small" label={get(attributeDetail, 'type')} />
                        }
                    />
                    <SchemaField label="Description" value={get(attributeDetail, 'description')} />
                    <SchemaField label="Created Date" value={formatDateTime(get(attributeDetail, 'createdAt'))} />
                    <SchemaField label="Updated Date" value={formatDateTime(get(attributeDetail, 'updatedAt'))} />
                    <SchemaField label="Created By" value={get(attributeDetail, 'createdBy')} />
                </Grid>
            </Grid>

            <AttributeSchemaDetail
                attribute={attributeDetail}
                qdtUnitMapper={qdtUnitMapper}
                qdtKindMapper={qdtKindMapper}
                schemaDetail={schemaDetail}
            />
        </>
    );
};
export default AttributeInformation;
