
import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
    AnimatedPage,
    Box,
    CloseIcon,
    ContentHeader,
    FixedHeightContainer,
    InputAdornment,
    SearchIcon,
    styled,
    tableStyles,
    TextField,
    Loading,
    DigitalThreadIcon,
    Typography,
} from 'ui-style';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { Link, Outlet } from 'react-router-dom';
import { AgGridReact } from '@ag-grid-community/react';
import { TreeContainer } from '@tripudiotech/admin-styleguide';
import isEmpty from 'lodash/isEmpty';
import debounce from 'lodash/debounce';

import { fetch, schemaUrls, buildContainsQuery, buildOrOperatorQuery } from '@tripudiotech/admin-api';
import { buildSortParams, buildQueryBasedOnFilter } from '@tripudiotech/admin-styleguide';

const ContentWrapper = styled(Box)(() => ({
    ...tableStyles,
    display: 'flex',
    height: '100%',
    '& .handle': {
        position: 'absolute',
        height: '100%',
        width: '8px',
        right: 0,
        top: 0,
    },
}));

const RowRenderer = ({ value, data }) => {
    return (
        <Link to={data.id} style={{ textDecoration: 'none', color: 'inherit' }}>
            <Box sx={{ width: '100%', display: 'flex', gap: '8px', cursor: 'pointer', alignItems: 'center' }}>
                <DigitalThreadIcon style={{ height: '16px', width: '16px' }} />
                <Typography variant="bo2">{value}</Typography>
            </Box>
        </Link>
    );
};

const AttributeLayout = () => {
    const [searchText, setSearchText] = useState('');
    const digitalThreadPanelRef = useRef<any>();
    const gridRef = useRef<AgGridReact>();
    const searchQuery = useRef('');

    const onSearchChanged = useCallback(
        debounce((e) => {
            const { value } = e.target;
            setSearchText(value);
            searchQuery.current = value;
            if (gridRef.current) {
                gridRef.current.api.refreshServerSide();
            }
        }, 400),
        []
    );

    const buildSearchQuery = useCallback((search) => {
        const searchText = search || '';
        return buildOrOperatorQuery([
            buildContainsQuery('name', searchText),
            buildContainsQuery('description', searchText),
        ]);
    }, []);

    const createServerSideDataSource = useCallback(() => {
        const buildParams = (params: any) => {
            let queryParams: any = {
                offset: params.startRow,
                limit: 50,
                sort: ['name,ASCENDING']
            };
            const filterModel = params.filterModel;
            if (Object.keys(filterModel).length > 0) {
                const filterConditions: any[] = [];
                if (searchQuery.current) {
                    filterConditions.push(buildSearchQuery(searchQuery.current));
                }
                queryParams['query'] = JSON.stringify(buildQueryBasedOnFilter(filterConditions, filterModel));
            } else if (searchQuery.current) {
                queryParams['query'] = JSON.stringify(buildSearchQuery(searchQuery.current));
            }
            return queryParams;
        };
        return {
            getRows: (params: any) => {
                gridRef.current?.api.showLoadingOverlay();
                fetch({
                    ...schemaUrls.getListAttribute,
                    qs: buildParams(params?.request),
                })
                    .then((response) => {
                        if (response.status !== 200) {
                            params.failCallback();
                            return;
                        }
                        const rowsThisPage = response.data.data;
                        const rowCount = response.data.pageInfo.total;
                        params.success({ rowData: rowsThisPage, rowCount });
                    })
                    .finally(() => {
                        gridRef.current?.api.hideOverlay();
                        if (gridRef.current?.api.getDisplayedRowCount() === 0) {
                            gridRef.current?.api.showNoRowsOverlay();
                        }
                    });
            },
        };
    }, [buildSearchQuery]);

    const gridOptions: any = useMemo(
        () => ({
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                filter: true,
                floatingFilter: false,
                flex: 1,
            },
            getRowId: (params: any) => {
                return params.data.id;
            },
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Name',
                    filter: 'agTextColumnFilter',
                    cellRenderer: RowRenderer,
                },
            ],
            rowModelType: 'serverSide',
            serverSideInfiniteScroll: true,
            cacheBlockSize: 50,
            rowSelection: 'single',
            getRowStyle: () => ({
                background: ' #FFFFFF',
                border: 'none',
            }),
            headerHeight: 0,
            rowHeight: 26,
            loadingOverlayComponent: Loading,
        }),
        []
    );

    const onGridReady = useCallback((params: any) => {
        const dataSource = createServerSideDataSource();
        params.api.setServerSideDatasource(dataSource);
    }, [createServerSideDataSource]);


    return (
        <AnimatedPage>
            <FixedHeightContainer>
                <ContentHeader
                    title="Attribute"
                />
                <ContentWrapper>
                    <PanelGroup direction="horizontal">
                        <Panel
                            id="digital-thread-list-panel"
                            defaultSize={20}
                            minSize={18}
                            collapsible
                            maxSize={80}
                            style={{ height: '100%', position: 'relative' }}
                            ref={digitalThreadPanelRef}
                        >
                            <TreeContainer>
                                <TextField
                                    className="searchBox"
                                    size="medium"
                                    value={searchText}
                                    onChange={onSearchChanged}
                                    fullWidth
                                    placeholder="Type to search"
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <SearchIcon />
                                            </InputAdornment>
                                        ),
                                        endAdornment: (
                                            <InputAdornment
                                                position="end"
                                                style={{
                                                    visibility: isEmpty(searchText) ? 'hidden' : 'visible',
                                                    cursor: 'pointer',
                                                }}
                                                onClick={() => setSearchText('')}
                                            >
                                                <CloseIcon sx={{ width: '16px', height: '16px' }} />
                                            </InputAdornment>
                                        ),
                                    }}
                                />
                                <div className="agContainer ag-theme-alpine">
                                    <AgGridReact
                                        {...gridOptions}
                                        ref={gridRef}
                                        onGridReady={onGridReady}
                                    />
                                </div>
                            </TreeContainer>
                            <PanelResizeHandle className="handle"></PanelResizeHandle>
                        </Panel>

                        <Panel id="digital-thread-detail-panel" style={{ height: '100%', position: 'relative' }}>
                            <Outlet />
                        </Panel>
                    </PanelGroup>
                </ContentWrapper>
            </FixedHeightContainer>
        </AnimatedPage>
    );
};

export default AttributeLayout;
