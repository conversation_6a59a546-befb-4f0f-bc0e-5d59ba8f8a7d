{"name": "@tripudiotech/admin-sidebar", "version": "1.0.0", "scripts": {"start": "webpack serve --port 9004", "start:standalone": "webpack serve --env standalone", "build": "concurrently yarn:build:*", "build:webpack": "webpack --mode=production", "lint": "eslint src --ext js,ts,tsx", "test": "cross-env BABEL_ENV=test jest", "watch-tests": "cross-env BABEL_ENV=test jest --watch", "coverage": "cross-env BABEL_ENV=test jest --coverage"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.15.0", "@babel/runtime": "^7.15.3", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^12.0.0", "@types/testing-library__jest-dom": "^5.14.1", "babel-jest": "^27.0.6", "concurrently": "^6.2.1", "cross-env": "^7.0.3", "css-loader": "^6.5.1", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-config-ts-react-important-stuff": "^3.0.0", "eslint-plugin-prettier": "^3.4.1", "identity-obj-proxy": "^3.0.0", "jest": "^27.0.6", "jest-cli": "^27.0.6", "lint-staged": "^13.0.2", "sass-loader": "^12.3.0", "style-loader": "^3.3.1", "ts-config-single-spa": "^3.0.0", "typescript": "^4.3.5", "webpack": "^5.75.0", "webpack-cli": "^4.10.0", "webpack-config-single-spa-react": "^4.0.0", "webpack-config-single-spa-react-ts": "^4.0.0", "webpack-config-single-spa-ts": "^4.0.0", "webpack-dev-server": "^4.0.0", "dagre": "^0.8.5", "webpack-merge": "^5.8.0"}, "dependencies": {"@popperjs/core": "^2.11.0", "@types/jest": "^27.0.1", "@types/react": "^17.0.19", "@types/react-dom": "^17.0.9", "@types/systemjs": "^6.1.1", "@types/webpack-env": "^1.16.2", "classnames": "^2.3.1", "file-loader": "^6.2.0", "react": "^17.0.2", "react-dom": "^17.0.2", "react-icons": "^4.3.1", "react-router": "^6.3.0", "react-router-dom": "^6.3.0", "single-spa": "^5.9.3", "single-spa-react": "^4.3.1", "styled-components": "^5.3.3", "ui-common": "npm:@tripudiotech/ui-common@^1.2.2", "ui-style": "file:../../../ui-style/build", "url-loader": "^4.1.1"}, "types": "dist/tripudiotech-sidebar.d.ts"}