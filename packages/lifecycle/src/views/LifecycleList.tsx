/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useRef, useMemo, useCallback, useEffect, useState } from 'react';
import {
    Box,
    Button,
    ContentHeader,
    FixedHeightContainer,
    styled,
    AnimatedPage,
    tableIcons,
    tableStyles,
    Loading,
    AGGridTablePagination,
    RichtextCellRenderer,
    AccountValue<PERSON>enderer,
} from 'ui-style';
import { buildSortParams, DEFAULT_TABLE_PAGINATION_SIZE, buildQueryBasedOnFilter, formatDateTime } from 'ui-common';
import { schemaUrls, fetch, getAvatarUrl } from '@tripudiotech/admin-api';
import { AgGridReact } from '@ag-grid-community/react';
import get from 'lodash/get';
import CreateLifecycleAction from '../components/actions/CreateLifecycle';
import { Outlet, useNavigate, useOutletContext } from 'react-router-dom';

const SEPARATORS = {
    DOT: '.',
    HYPHEN: '-',
    UNDER_SCORE: '_',
};

const ContentWrapper = styled(Box)(({ theme }) => ({
    ...tableStyles,
    display: 'flex',
    height: '100%',
    width: '100%',
}));

const LifecycleList = () => {
    // Pagination
    const [totalRows, setTotalRows] = useState<number>(0);
    const gridRef = useRef<AgGridReact>(null);
    const navigate = useNavigate();

    const onRowClicked = (e) => {
        const id = e.data.id;
        navigate(`/lifecycle/${id}/lifecycle`);
    };
    const gridOptions: any = useMemo(() => {
        return {
            headerHeight: 34,
            rowHeight: 34,
            loadingOverlayComponent: Loading,
            animateRows: true,
            defaultColDef: {
                sortable: true,
                resizable: true,
                flex: 1,
                filter: true,
                floatingFilter: false,
                cellStyle: () => ({
                    display: 'block',
                }),
            },
            getRowId: (params) => {
                return params.data.id;
            },
            columnDefs: [
                {
                    field: 'name',
                    headerName: 'Rule Name',
                    filter: 'agTextColumnFilter',
                },
                {
                    field: 'description',
                    headerName: 'Description',
                    filter: 'agTextColumnFilter',
                    cellRenderer: RichtextCellRenderer,
                },
                {
                    field: 'revisionPattern',
                    headerName: 'Revision Pattern',
                    minWidth: 110,
                    cellRenderer: (props) => {
                        const value = props.getValue();

                        if (Boolean(value)) {
                            const { major, minor, patch, separator } = value;
                            return [major, minor, patch].filter(Boolean).join(SEPARATORS[separator] || '');
                        }
                        return '';
                    },
                    cellStyle: () => ({
                        color: '#52C41A',
                    }),
                },
                {
                    field: 'createdAt',
                    headerName: 'Created At',
                    valueGetter: (params) => {
                        return formatDateTime(get(params.data, 'updatedAt'));
                    },
                    minWidth: 110,
                    filter: 'agDateColumnFilter',
                    filterParams: {
                        defaultOption: 'inRange',
                        comparator: function (filterLocalDate, cellValue) {
                            filterLocalDate = new Date(filterLocalDate);
                            cellValue = new Date(cellValue);
                            let filterBy = filterLocalDate.getTime();
                            let filterMe = cellValue.getTime();
                            if (filterBy === filterMe) {
                                return 0;
                            }

                            if (filterMe < filterBy) {
                                return -1;
                            }

                            if (filterMe > filterBy) {
                                return 1;
                            }
                        },
                    },
                },
                {
                    field: 'createdBy',
                    headerName: 'Created By',
                    minWidth: 110,
                    cellRenderer: AccountValueRenderer,
                    cellRendererParams: {
                        getAvatarUrl: getAvatarUrl,
                    },
                },
            ],
            icons: tableIcons,
            cacheBlockSize: DEFAULT_TABLE_PAGINATION_SIZE,
            rowModelType: 'serverSide',
            serverSideInfiniteScroll: true,
            paginationNumberFormatter: null,
            onRowClicked,
            rowSelection: 'single',
        };
    }, [gridRef]);

    const createServerSideDataSource = useCallback(
        (event) => {
            const buildParams = (params) => {
                const filterModel = params.filterModel;

                let queryParams = {
                    offset: params.startRow || 0,
                    limit: event.api.paginationGetPageSize(),
                    ...buildSortParams(params.sortModel),
                };
                if (filterModel && Object.keys(filterModel).length > 0) {
                    const filterConditions = [];
                    queryParams['query'] = JSON.stringify(buildQueryBasedOnFilter(filterConditions, filterModel));
                }
                return queryParams;
            };
            return {
                getRows: (params) => {
                    gridRef.current.api.showLoadingOverlay();
                    fetch({
                        ...schemaUrls.getListLifeCycle,
                        qs: buildParams(params?.request),
                    })
                        .then((response) => {
                            if (response.status !== 200) {
                                params.failCallback();
                                return;
                            }
                            const rowsThisPage = response.data.data;
                            const rowCount = response.data.pageInfo.total;
                            params.success({ rowData: rowsThisPage, rowCount });
                            setTotalRows(rowCount);
                        })
                        .finally(() => {
                            gridRef.current.api.hideOverlay();
                            if (gridRef.current.api.getDisplayedRowCount() === 0) {
                                gridRef.current.api.showNoRowsOverlay();
                            }
                        });
                },
            };
        },
        [gridRef, setTotalRows]
    );

    const handleSetDataSource = useCallback((event) => {
        const dataSource = createServerSideDataSource(event);
        event.api.setServerSideDatasource(dataSource);
    }, []);

    const onRefresh = useCallback(() => {
        gridRef.current.api.refreshServerSide();
    }, []);

    return (
        <AnimatedPage>
            <FixedHeightContainer>
                <ContentHeader
                    title="Lifecycle"
                    actionsRenderer={() => (
                        <Box sx={{ display: 'flex', gap: '8px', ml: 'auto' }}>
                            <CreateLifecycleAction onRefresh={onRefresh} />
                        </Box>
                    )}
                />
                <ContentWrapper sx={{ ...tableStyles, '& .ag-paging-panel': { display: 'none' } }}>
                    <div className="ag-theme-alpine" style={{ width: '100%' }}>
                        <AgGridReact ref={gridRef} {...gridOptions} onGridReady={handleSetDataSource} pagination />
                    </div>
                    <div>
                        <Outlet context={{ onRefresh }} />
                    </div>
                </ContentWrapper>
                <AGGridTablePagination gridRef={gridRef} totalRows={totalRows} />
            </FixedHeightContainer>
        </AnimatedPage>
    );
};

export default LifecycleList;

type ContextType = { onRefresh: () => void };

export const useOnRefresh = () => {
    return useOutletContext<ContextType>();
};
