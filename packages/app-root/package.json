{"name": "@tripudiotech/admin-root-config", "version": "1.0.0", "scripts": {"start": "webpack serve --port 9000", "lint": "eslint src --ext js,ts,tsx", "test": "cross-env BABEL_ENV=test jest --passWithNoTests", "build": "concurrently yarn:build:*", "build:webpack": "webpack --mode=production"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.15.0", "@babel/runtime": "^7.15.3", "concurrently": "^6.2.1", "copy-webpack-plugin": "^10.2.4", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-ts-important-stuff": "^1.1.0", "html-webpack-plugin": "^5.3.2", "jest": "^27.0.6", "jest-cli": "^27.0.6", "lint-staged": "^13.0.2", "serve": "^14.2.0", "ts-config-single-spa": "^3.0.0", "typescript": "^4.3.5", "webpack": "^5.75.0", "webpack-cli": "^4.10.0", "webpack-config-single-spa-ts": "^4.0.0", "webpack-dev-server": "^4.0.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@types/jest": "^27.0.1", "@types/react": "^17.0.19", "@types/react-dom": "^17.0.9", "@types/systemjs": "^6.1.1", "@types/webpack-env": "^1.16.2", "classnames": "^2.3.1", "dotenv": "^11.0.0", "keycloak-js": "^25.0.1", "react": "^17.0.2", "react-dom": "^17.0.2", "single-spa": "^5.9.3", "single-spa-layout": "^2.0.1", "tinycolor2": "^1.4.2"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --no-ignore --fix", "git add --force"], "*.{json,md,js,jsx,ts,tsx}": ["prettier --write", "git add --force"]}, "types": "dist/tripudiotech-root-config.d.ts"}