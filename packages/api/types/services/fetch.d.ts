/// <reference types="react" />
import { type RawAxiosRequestHeaders, type ResponseType } from 'axios';
export declare enum Method {
    GET = "GET",
    POST = "POST",
    PUT = "PUT",
    DELETE = "DELETE",
    PATCH = "PATCH"
}
export declare const invariant: (cond: any, message: string) => void;
export declare const generatePath: (path: any, params: any) => string;
interface FetchProps {
    url: string;
    params?: Record<string, any>;
    method: Method;
    qs?: Record<string, any>;
    data?: Record<string, any> | string;
    successMessage?: string | JSX.Element;
    errorMessage?: string | JSX.Element;
    shouldShow404?: boolean;
    shouldShow403?: boolean;
    skipToast?: boolean;
    skipDetails?: boolean;
    headers?: RawAxiosRequestHeaders;
    signal?: AbortSignal;
    responseType?: ResponseType;
    onUploadProgress?: (progressEvent: any) => void;
    ignoreErrorCodes?: number[];
}
declare const fetch: ({ url, params, method, qs, data, successMessage, errorMessage, shouldShow404, shouldShow403, skipToast, skipDetails, headers, signal, responseType, ignoreErrorCodes, }: FetchProps) => Promise<import("axios").AxiosResponse<any, any>>;
export default fetch;
//# sourceMappingURL=fetch.d.ts.map