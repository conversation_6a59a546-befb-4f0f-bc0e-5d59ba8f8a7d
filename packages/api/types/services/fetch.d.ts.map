{"version": 3, "file": "fetch.d.ts", "sourceRoot": "", "sources": ["../../src/services/fetch.ts"], "names": [], "mappings": ";AAiBA,OAAc,EAAE,KAAK,sBAAsB,EAAE,KAAK,YAAY,EAAE,MAAM,OAAO,CAAC;AAG9E,oBAAY,MAAM;IACd,GAAG,QAAQ;IACX,IAAI,SAAS;IACb,GAAG,QAAQ;IACX,MAAM,WAAW;IACjB,KAAK,UAAU;CAClB;AAED,eAAO,MAAM,SAAS,uBAAmB,MAAM,SAE9C,CAAC;AAEF,eAAO,MAAM,YAAY,8BAAmB,MAO3C,CAAC;AAEF,UAAU,UAAU;IAChB,GAAG,EAAE,MAAM,CAAC;IACZ,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC7B,MAAM,EAAE,MAAM,CAAC;IACf,EAAE,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACzB,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC;IACpC,cAAc,CAAC,EAAE,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC;IACtC,YAAY,CAAC,EAAE,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC;IACpC,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,OAAO,CAAC,EAAE,sBAAsB,CAAC;IACjC,MAAM,CAAC,EAAE,WAAW,CAAC;IACrB,YAAY,CAAC,EAAE,YAAY,CAAC;IAC5B,gBAAgB,CAAC,EAAE,CAAC,aAAa,EAAE,GAAG,KAAK,IAAI,CAAC;IAChD,gBAAgB,CAAC,EAAE,MAAM,EAAE,CAAC;CAC/B;AAED,QAAA,MAAM,KAAK,4KAgBR,UAAU,qDAuDZ,CAAC;AAEF,eAAe,KAAK,CAAC"}