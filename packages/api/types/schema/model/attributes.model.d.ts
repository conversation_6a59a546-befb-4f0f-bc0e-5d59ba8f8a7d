import { type AttributeConstraint } from './attribute-constraint.model';
import AttributeType from './attribute-type.enum';
interface Identifier {
    nextAssignment: string;
    prefix: string;
    skipCharacters: string;
    suffix: string;
}
export default interface Attribute {
    id: string;
    updateAt: string;
    createdAt: string;
    disabled: boolean;
    constraint?: AttributeConstraint;
    description: string;
    name: string;
    displayName: string;
    nullable: boolean;
    system: boolean;
    type: AttributeType;
    defaultValue: any;
    visible: boolean;
    identifier?: Identifier;
    hyperlink: boolean;
}
export {};
//# sourceMappingURL=attributes.model.d.ts.map