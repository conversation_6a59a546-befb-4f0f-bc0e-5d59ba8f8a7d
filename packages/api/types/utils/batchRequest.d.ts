import { Method } from '../services/fetch';
declare const createBatchReqData: (method: Method, url: string, data: {
    subParams: any;
    body: any;
    subRequests: any[];
    url: string;
    method: any;
}[]) => {
    method: any;
    url: string;
    body: any;
    subRequests: {
        url: string;
        method: any;
        body: any;
    }[];
}[];
export declare const batchRequest: ({ url, method, data, maxChunkRequest }: {
    url: any;
    method: any;
    data: any;
    maxChunkRequest?: number;
}) => Promise<{
    success: boolean;
    data: import("axios").AxiosResponse<any, any>[];
    error?: undefined;
} | {
    success: boolean;
    error: any;
    data?: undefined;
}>;
export declare const handleBatchRequestRes: ({ response: { success, data }, statusCodeOnSuccess, onSuccess, onFailed, successMessage, failedMessage, }: {
    response: {
        success: any;
        data: any;
    };
    statusCodeOnSuccess?: number;
    onSuccess?: () => void;
    onFailed?: () => void;
    successMessage?: "Successfully executed the request";
    failedMessage?: "An error occurred while executing the request. Please try again later";
}) => void;
export default createBatchReqData;
//# sourceMappingURL=batchRequest.d.ts.map