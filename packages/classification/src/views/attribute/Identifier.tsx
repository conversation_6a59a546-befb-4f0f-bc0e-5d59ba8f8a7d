/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useCallback, useMemo, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import { ISchemaDetail, UI_SEARCH_PARAM, SCHEMA_VIEW, schema<PERSON><PERSON>per, IDialog, ACTION } from 'ui-common';
import { RightTray, Box, Button, FormikTextField } from 'ui-style';
import { COMPONENT_NAME } from '../../constants/component-name';
import get from 'lodash/get';
import * as yup from 'yup';
import { Formik, Form, Field } from 'formik';
import { useDialog, useSchemaDetail } from '@tripudiotech/admin-caching-store';
import useLocalStore from '../../store/useLocalStore';
import { createIdentifier, deleteIdentifier, updateIdentifier } from '../../actions';

const validationSchema = yup.object().shape({
    nextAssignment: yup.string().required('Next assignment is required'),
    prefix: yup.string().nullable(),
    suffix: yup.string().nullable(),
    skipCharacters: yup.string().nullable(),
});

const Identifier = ({ schemaDetail }: { schemaDetail: ISchemaDetail }) => {
    const getSchema = useSchemaDetail((state) => state.getSchema);
    const formRef = useRef(null);

    // url params
    const [searchParams, setSearchParams] = useSearchParams();
    const attributeName = searchParams.get(UI_SEARCH_PARAM.ATTRIBUTE);
    const open = attributeName && searchParams.get(UI_SEARCH_PARAM.VIEW) === SCHEMA_VIEW.IDENTIFIER;
    const action = searchParams.get(UI_SEARCH_PARAM.ACTION);
    const isUpdating = action === ACTION.UPDATE;

    const onOpenDialog = useDialog((state: IDialog) => state.onOpenDialog);
    const setIsLoading = useLocalStore((state) => state.setIsLoading);

    const selectedAttribute = useMemo(() => {
        return get(schemaDetail, ['attributes', attributeName]);
    }, [schemaDetail, attributeName]);

    const disabled = get(selectedAttribute, 'system') && get(selectedAttribute, 'name') !== 'name';

    const initialValues = useMemo(() => {
        return isUpdating
            ? {
                  ...(selectedAttribute?.identifier || {}),
              }
            : {
                  nextAssignment: '',
                  prefix: '',
                  suffix: '',
                  skipCharacters: '',
              };
    }, [selectedAttribute]);

    const handleClose = useCallback(() => {
        // clear all search params
        schemaHelper.closeIdentifier(searchParams, setSearchParams);
    }, [searchParams, setSearchParams]);

    const handleCreate = useCallback(
        async (values) => {
            setIsLoading(true);
            await createIdentifier(schemaDetail.entityType.name, selectedAttribute.name, values);
            await getSchema(schemaDetail.entityType.name);
            setIsLoading(false);
            handleClose();
        },
        [schemaDetail, selectedAttribute]
    );

    const handleUpdate = useCallback(
        async (values) => {
            setIsLoading(true);
            await updateIdentifier(schemaDetail.entityType.name, selectedAttribute.name, values);
            await getSchema(schemaDetail.entityType.name);
            setIsLoading(false);
            handleClose();
        },
        [schemaDetail, selectedAttribute]
    );

    const handleSubmit = useCallback(
        (values) => {
            if (formRef.current.dirty) {
                if (isUpdating) {
                    handleUpdate(values);
                } else {
                    handleCreate(values);
                }
            } else {
                handleClose();
            }
        },
        [selectedAttribute, handleClose]
    );

    const handleDeleteIdentifier = useCallback(async () => {
        setIsLoading(true);
        const res = await deleteIdentifier(schemaDetail.entityType.name, selectedAttribute.name);
        if (res) {
            await getSchema(schemaDetail.entityType.name);
            setIsLoading(false);
            handleClose();
        }
        setIsLoading(false);
    }, [schemaDetail, selectedAttribute, handleClose]);

    const onDelete = useCallback(() => {
        onOpenDialog(
            'Delete auto generated value',
            `Do you really want to delete the <b>auto generated value</b> on attribute <b>${selectedAttribute.displayName}</b>?`,
            handleDeleteIdentifier,
            'error'
        );
    }, [schemaDetail, selectedAttribute, handleDeleteIdentifier]);

    return (
        <RightTray
            title={`Auto Generated Value of ${attributeName}`}
            componentName={COMPONENT_NAME.EDIT_METADATA}
            open={Boolean(open)}
            onClose={handleClose}
            confirmText={isUpdating ? 'Save changes' : 'Create'}
            disableCloseOnClickOutside
            hideConfirm
        >
            <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%', p: '0px 24px', pb: '16px' }}>
                <Box sx={{ overflow: 'auto', pt: '16px' }}>
                    <Formik
                        enableReinitialize
                        initialValues={initialValues}
                        validationSchema={validationSchema}
                        onSubmit={(values, { setSubmitting }) => {
                            handleSubmit(values);
                            setSubmitting(false);
                        }}
                    >
                        <Formik
                            enableReinitialize
                            initialValues={initialValues}
                            innerRef={formRef}
                            validationSchema={validationSchema}
                            onSubmit={(values, { setSubmitting }) => {
                                handleSubmit(values as any);
                                setSubmitting(false);
                            }}
                        >
                            {({ values, setFieldValue, ...rest }) => {
                                return (
                                    <Form
                                        id="identifier-form"
                                        style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}
                                    >
                                        <Field
                                            fullWidth
                                            component={FormikTextField}
                                            label="Next Assignment"
                                            name="nextAssignment"
                                            variant="outlined"
                                        />
                                        <Box sx={{ display: 'flex', gap: '12px' }}>
                                            <Field
                                                fullWidth
                                                component={FormikTextField}
                                                label="Prefix"
                                                name="prefix"
                                                variant="outlined"
                                            />
                                            <Field
                                                fullWidth
                                                component={FormikTextField}
                                                label="Suffix"
                                                name="suffix"
                                                variant="outlined"
                                            />
                                        </Box>
                                        <Field
                                            fullWidth
                                            component={FormikTextField}
                                            label="Skip Characters"
                                            name="skipCharacters"
                                            variant="outlined"
                                        />
                                    </Form>
                                );
                            }}
                        </Formik>
                    </Formik>
                </Box>
                <Box sx={{ mt: 'auto', display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            mr: '8px',
                            maxWidth: '160px',
                        }}
                        variant="contained"
                        color="secondary"
                        onClick={handleClose}
                    >
                        Cancel
                    </Button>
                    {isUpdating && (
                        <Button
                            sx={{
                                width: '136px',
                                justifyContent: 'flex-start',
                            }}
                            variant="outlined"
                            onClick={onDelete}
                            color="error"
                            disabled={disabled}
                        >
                            Delete
                        </Button>
                    )}
                    <Button
                        sx={{
                            width: '136px',
                            justifyContent: 'flex-start',
                            maxWidth: '260px',
                        }}
                        variant="contained"
                        onClick={() => formRef.current.submitForm()}
                        color="primary"
                        disabled={disabled}
                    >
                        Save
                    </Button>
                </Box>
            </Box>
        </RightTray>
    );
};

export default Identifier;
