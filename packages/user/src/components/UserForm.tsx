/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    styled,
    Typography,
    FormikTextField,
    LocalizationProvider,
    DatePicker,
    AdapterDateFns,
    LoadingOverlay,
    RichTextField,
    TextField,
    Autocomplete,
} from 'ui-style';
import { Formik, Form, Field } from 'formik';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import isNil from 'lodash/isNil';
import debounce from 'lodash/debounce';
import { AttributeType, formatDateStringToDate, formatDate } from '@tripudiotech/admin-styleguide';
import { useOrg, useRoles, useSchemaDetail } from '@tripudiotech/admin-caching-store';
import useLocalStore from '../store/useLocalStore';
import get from 'lodash/get';
import * as yup from 'yup';
import {
    filterAndSortAttributesForEdit,
    RESPONSE_ERROR_TYPE,
    SYSTEM_ENTITY_TYPE,
    AxiosResponse,
} from '@tripudiotech/admin-api';
import { AttributeSchema } from 'ui-common';
import orderBy from 'lodash/orderBy';

const FormContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    marginBottom: 'auto',
    '& .generalInfo': {
        display: 'flex',
        flexDirection: 'column',
        gap: '12px',
        padding: '12px',
    },
    '& .sectionLabel': {
        color: theme.palette.info.main,
    },
}));

interface DynamicAttributesProps {
    attributes: AttributeSchema[];
    setFieldValue: (field: string, value: any, shouldValidate?: boolean) => Promise<any>;
    disableAttr: string[];
    showOnlyPastDates: string[];
}

const DynamicAttributes = ({
    attributes,
    setFieldValue,
    disableAttr,
    showOnlyPastDates = [],
}: DynamicAttributesProps) => {
    return (
        <>
            {attributes.map((attribute) => {
                if (!attribute.visible || !attribute.mutable) return;
                const { id, displayName, name, description, nullable } = attribute;
                const isRequired = isNil(nullable) ? false : !Boolean(nullable);
                if (attribute.richText) {
                    return (
                        <Field
                            key={id}
                            fullWidth
                            variant="outlined"
                            id={id}
                            label={displayName}
                            name={name}
                            helperText={description}
                            disabled={disableAttr.includes(name)}
                            required={isRequired}
                            InputLabelProps={{ shrink: true }}
                            component={RichTextField}
                        />
                    );
                }
                switch (attribute.type) {
                    case AttributeType.STRING:
                        return (
                            <Field
                                key={id}
                                fullWidth
                                variant="outlined"
                                component={FormikTextField}
                                required={isRequired}
                                label={displayName}
                                name={name}
                                helperText={description}
                                disabled={disableAttr.includes(name)}
                                InputLabelProps={{ shrink: true }}
                            />
                        );
                    case AttributeType.DATE:
                        return (
                            <Field name={name} helperText={description} key={id}>
                                {({ field, form, meta: { error, touched } }) => {
                                    const isErr = touched && !!error;
                                    return (
                                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                                            <DatePicker
                                                label={displayName}
                                                value={formatDateStringToDate(field.value, 'yyyy-MM-DD')}
                                                onChange={(newValue) => {
                                                    if (newValue?.toString() !== 'Invalid Date') {
                                                        setFieldValue(
                                                            name,
                                                            // Note: Don't change the format. Backend is expecting YYYY-MM-DD format. This format must match the value parsing format above
                                                            // Otherwise it breaks editing the date
                                                            formatDate(newValue?.toISOString(), 'yyyy-MM-DD'),
                                                            true
                                                        );
                                                    }
                                                }}
                                                disabled={disableAttr.includes(name)}
                                                renderInput={(params) => (
                                                    <TextField
                                                        {...params}
                                                        InputLabelProps={{ shrink: true }}
                                                        error={isErr}
                                                        required={isRequired}
                                                        helperText={isErr ? error : description}
                                                        fullWidth
                                                        onBlur={(e) => {
                                                            form.setFieldTouched(name);
                                                            const newValue = e.target.value;
                                                            // set the value typed even if it is invalid so that invalid dates are captured and validated by yup
                                                            // and error message is shown
                                                            setFieldValue(name, newValue, true);
                                                        }}
                                                    />
                                                )}
                                                disableFuture={showOnlyPastDates.includes(attribute.name)}
                                                inputFormat="yyyy-MM-dd" // Note: different date format betweeen picker and moment. For moment, equivalent is yyyy-MM-DD
                                            />
                                        </LocalizationProvider>
                                    );
                                }}
                            </Field>
                        );
                }
            })}
        </>
    );
};

const UserForm = ({ formRef, handleSubmit, isCreateNew = false }) => {
    const { assignedRoles, assignedTeams, assignedDepartments, userInfo } = useLocalStore();
    const { roles, getRoles } = useRoles();
    const [userRoles, setUserRole] = useState(assignedRoles);
    const [userDepartments, setUserDepartments] = useState(assignedDepartments);
    const [userTeams, setUserTeams] = useState(assignedTeams);

    const { searchCompany, getDepartmentsByCompany, getTeamsByCompany, getCompaniesMapFromCompanies } = useOrg();
    const [companies, setCompanies] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [teams, setTeams] = useState([]);
    const companiesMap = useMemo(() => getCompaniesMapFromCompanies(companies), [companies]);

    const companySerchTextRef = useRef('');
    const defaultCompanyRef = useRef(null);

    const [personSchema, getSchema, getAttributeName] = useSchemaDetail((state) => [
        state.schema[SYSTEM_ENTITY_TYPE.PERSON],
        state.getSchema,
        state.getAttributeName,
    ]);
    const attributes = useMemo(() => {
        return filterAndSortAttributesForEdit(
            Object.values(personSchema?.attributes || {}),
            personSchema?.entityType?.attributeOrder
        );
    }, [personSchema]);

    const onRoleChange = (_, newValue) => {
        setUserRole(newValue);
    };

    const onDepartmentChange = (_, newValue) => {
        setUserDepartments(newValue);
    };

    const onTeamChange = (_, newValue) => {
        setUserTeams(newValue);
    };

    const getCompanies = useCallback((searchText = '') => {
        searchCompany(searchText).then((data) => {
            setCompanies(data);
        });
    }, []);

    const getDepartments = useCallback(
        (companyId: string, searchText = '') =>
            getDepartmentsByCompany(companyId, searchText).then((data) => {
                setDepartments(orderBy(data, (item) => item.properties?.name?.toLowerCase()));
            }),
        []
    );

    const getTeams = useCallback(
        (companyId: string, searchText = '') =>
            getTeamsByCompany(companyId, searchText).then((data) => {
                setTeams(orderBy(data, (item) => item.properties?.name?.toLowerCase()));
            }),
        []
    );

    useEffect(() => {
        getRoles();
        getSchema(SYSTEM_ENTITY_TYPE.PERSON);
    }, []);

    const debouncedFetchCompanies = useMemo(() => debounce(getCompanies, 300), [getCompanies]);
    const debouncedFetchDepartments = useMemo(() => debounce(getDepartments, 300), [getDepartments]);
    const debouncedFetchTeams = useMemo(() => debounce(getTeams, 300), [getTeams]);

    const validationSchema = useMemo(() => {
        if (!personSchema) return null;
        const validateRule = {
            companyId: yup.string().required('Company is required'),
        };
        attributes.forEach((attribute) => {
            if (attribute.nullable) return;
            if (!validateRule[attribute.name]) {
                validateRule[attribute.name] = yup.string().required(`${attribute.displayName} is required`);
            }
        });
        return yup.object().shape(validateRule);
    }, [personSchema]);

    const initialValues = useMemo(() => {
        if (!personSchema) return null;
        if (isCreateNew) {
            const initValue = {
                companyId: '',
            };
            attributes.forEach((attribute) => {
                initValue[attribute.name] = '';
            });
            return initValue;
        }
        const initial = {
            companyId: get(userInfo, 'companyId'),
        };

        defaultCompanyRef.current = {
            id: get(userInfo, 'companyId'),
            name: get(userInfo, 'owner.name'),
        };

        attributes.forEach((attribute) => {
            initial[attribute.name] = get(userInfo, `properties.${attribute.name}`);
        });
        return initial;
    }, [personSchema, userInfo]);

    const handleCompanyChange = (companyId) => {
        getDepartments(companyId);
        getTeams(companyId);
        setUserDepartments([]);
        setUserTeams([]);
    };

    const handleResetCompany = () => {
        formRef.current?.setFieldValue('companyId', '');
        setUserDepartments([]);
        setUserTeams([]);
    };

    const handleCompanyInputChange = (event, newValue) => {
        companySerchTextRef.current = newValue;
        if (event?.type === 'change' || event?.type === 'blur') {
            handleResetCompany();
            if (companySerchTextRef.current.length >= 2) {
                debouncedFetchCompanies(newValue);
            }
        }
    };

    const handleDepartmentInputChange = (event, newValue) => {
        const companyId = formRef.current?.values?.companyId;
        if (event?.type === 'change' || event?.type === 'blur') {
            debouncedFetchDepartments(companyId, newValue);
        }
    };

    const handleTeamInputChange = (event, newValue) => {
        const companyId = formRef.current?.values?.companyId;
        if (event?.type === 'change' || event?.type === 'blur') {
            debouncedFetchTeams(companyId, newValue);
        }
    };

    // clean up debounce functions on unmount
    useEffect(() => {
        return () => {
            debouncedFetchCompanies.cancel();
        };
    }, [debouncedFetchCompanies]);

    useEffect(() => {
        return () => {
            debouncedFetchDepartments.cancel();
        };
    }, [debouncedFetchDepartments]);

    useEffect(() => {
        return () => {
            debouncedFetchTeams.cancel();
        };
    }, [debouncedFetchTeams]);

    const onError = (error: AxiosResponse) => {
        if (error.status === 400 && error.data?.metadata?.type === RESPONSE_ERROR_TYPE.INVALID_ATTRIBUTE_VALUE) {
            const invalidAttributeId = error.data.metadata.data;
            const invalidAttributeName = getAttributeName(personSchema, invalidAttributeId);
            if (invalidAttributeName && formRef.current) {
                formRef.current.setFieldError(invalidAttributeName, error.data.errorMessage);
            }
        }
    };

    return (
        <>
            {(!companies || !personSchema) && <LoadingOverlay />}
            <FormContainer>
                <Formik
                    enableReinitialize
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={(values, { setSubmitting }) => {
                        handleSubmit(values, userRoles, userDepartments, userTeams, onError);
                        setSubmitting(false);
                    }}
                    innerRef={formRef}
                >
                    {({ setFieldValue, values, errors }) => (
                        <Form>
                            <div className="generalInfo">
                                <Typography variant="label2-med" className="sectionLabel">
                                    General Information
                                </Typography>
                                <Field name={'companyId'} helperText={'Company under which the user will be created'}>
                                    {({ field, form, meta: { error, touched } }) => {
                                        const isErr = touched && !!error;
                                        return (
                                            <Autocomplete
                                                disablePortal
                                                options={companies ?? []}
                                                getOptionLabel={(option: any) =>
                                                    option?.properties?.name || option?.name || ''
                                                }
                                                isOptionEqualToValue={(option: any, value: any) =>
                                                    option?.id === value?.id
                                                }
                                                id="companyId"
                                                value={
                                                    values?.companyId
                                                        ? companiesMap?.[values.companyId] ?? defaultCompanyRef.current
                                                        : null
                                                }
                                                onChange={(_, newValue) => {
                                                    setFieldValue('companyId', newValue?.id);
                                                    handleCompanyChange(newValue?.id);
                                                }}
                                                onInputChange={handleCompanyInputChange}
                                                disabled={!isCreateNew}
                                                renderInput={(params) => (
                                                    <TextField
                                                        {...params}
                                                        variant="outlined"
                                                        label="Company"
                                                        size="small"
                                                        required
                                                        InputLabelProps={{ shrink: true }}
                                                        error={isErr}
                                                        helperText={
                                                            error ?? 'Company under which the user will be created'
                                                        }
                                                    />
                                                )}
                                                noOptionsText={
                                                    companySerchTextRef?.current.length < 2
                                                        ? 'Enter at least two characters for searching'
                                                        : 'No Options'
                                                }
                                            />
                                        );
                                    }}
                                </Field>
                                <Autocomplete
                                    options={departments ? departments : []}
                                    multiple
                                    getOptionLabel={(option: any) => option?.properties?.name || option?.name}
                                    isOptionEqualToValue={(option: any, value: any) => option?.id === value?.id}
                                    id="department"
                                    value={userDepartments}
                                    onChange={onDepartmentChange}
                                    onInputChange={handleDepartmentInputChange}
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            variant="outlined"
                                            label="Department"
                                            size="small"
                                            InputLabelProps={{ shrink: true }}
                                        />
                                    )}
                                />
                                <Autocomplete
                                    options={teams ? teams : []}
                                    multiple
                                    getOptionLabel={(option: any) => option?.properties?.name || option?.name}
                                    isOptionEqualToValue={(option: any, value: any) => option?.id === value?.id}
                                    id="team"
                                    value={userTeams}
                                    onChange={onTeamChange}
                                    onInputChange={handleTeamInputChange}
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            variant="outlined"
                                            label="Team"
                                            size="small"
                                            InputLabelProps={{ shrink: true }}
                                        />
                                    )}
                                />
                                <DynamicAttributes
                                    attributes={attributes}
                                    setFieldValue={setFieldValue}
                                    disableAttr={isCreateNew ? [] : ['email']}
                                    showOnlyPastDates={[]}
                                />
                                <Autocomplete
                                    options={roles ? roles : []}
                                    multiple
                                    getOptionLabel={(option: any) => option.name}
                                    isOptionEqualToValue={(option: any, value: any) => option.id === value.id}
                                    id="role"
                                    value={userRoles}
                                    onChange={onRoleChange}
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            variant="outlined"
                                            label="Role"
                                            size="small"
                                            InputLabelProps={{ shrink: true }}
                                        />
                                    )}
                                />
                            </div>
                        </Form>
                    )}
                </Formik>
            </FormContainer>
        </>
    );
};

export default UserForm;
