type UnitOfMeasureState = {
    quantityKind?: Record<string, any>;
    quantityUnit?: Record<string, any>;
    expiredAt?: number;
    updateQuantityUnit?: (key: string, value: string) => void;
    qdtUnitMapper: (groupId?: string, unit?: string) => string;
    qdtKindMapper: (groupId?: string) => string;
    setQuantityKind: (quantityKind: any[]) => void;
    getQuantityKind: () => void;
    getQuantityUnit: (groupId: string) => void;
};
declare const useUnitOfMeasure: import("zustand").UseBoundStore<Omit<import("zustand").StoreApi<UnitOfMeasureState>, "persist"> & {
    persist: {
        setOptions: (options: Partial<import("zustand/middleware").PersistOptions<UnitOfMeasureState, UnitOfMeasureState>>) => void;
        clearStorage: () => void;
        rehydrate: () => void | Promise<void>;
        hasHydrated: () => boolean;
        onHydrate: (fn: (state: UnitOfMeasureState) => void) => () => void;
        onFinishHydration: (fn: (state: UnitOfMeasureState) => void) => () => void;
        getOptions: () => Partial<import("zustand/middleware").PersistOptions<UnitOfMeasureState, UnitOfMeasureState>>;
    };
}>;
export default useUnitOfMeasure;
//# sourceMappingURL=useUnitOfMeasure.d.ts.map