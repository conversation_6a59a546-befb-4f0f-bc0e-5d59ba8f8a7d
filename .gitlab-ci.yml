stages:
  - install
  - package
  - publish
  - invalidate

default:
  tags:
    - glide-runner

image: public.ecr.aws/m6s1b6b2/ci-image:latest

.aws-stag-variables:
  variables:
    CF_DISTRIBUTIONS_ID: EDCFONAMC0IR9
    BUCKET_NAME: ui-stage.glideyoke.com
    BASE_URL: https://api-stage.glideyoke.com
    AUTH_SERVICE_URL: "https://auth-stage.glideyoke.com"
    IDENTITY_SERVICE_URL: "https://identity-stage.glideyoke.com"
    MASTER_DOMAIN: "ui-stage"
    MEDIA_URL: "https://media-stage.glideyoke.com"
    DEFAULT_TENANT_ID: "master"
    TRACKING_SERVICE_URL: "https://api-stage.glideyoke.com/tracking"
    DOMAIN_PATTERN: "(\\w.+)"
  rules:
    - if: $CI_COMMIT_BRANCH == "main" || $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - $PACKAGE_DIR/**/*

.gcp-stag-variables:
  variables:
    GCP_PROJECT_ID: level-ruler-447003-u3
    URL_MAP_NAME: glikeyoke-ui-alb
    BUCKET_NAME: glikeyoke-ui-bucket
    BASE_URL: https://api-stage.gcp.glideyoke.com
    AUTH_SERVICE_URL: "https://auth-stage.gcp.glideyoke.com"
    IDENTITY_SERVICE_URL: "https://identity-stage.gcp.glideyoke.com"
    MASTER_DOMAIN: "ui-stage"
    MEDIA_URL: "https://media-stage.gcp.glideyoke.com"
    DEFAULT_TENANT_ID: "master"
    TRACKING_SERVICE_URL: "https://api-stage.gcp.glideyoke.com/tracking"
    DOMAIN_PATTERN: "(\\w.+)"
  rules:
    - if: $CI_COMMIT_BRANCH == "main" || $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - $PACKAGE_DIR/**/*
    - if: $CI_PIPELINE_SOURCE == "pipeline" && $PIPELINE_ID == "UAT" && $ENV_ID == "GCP_Staging"
    - if: $CI_PIPELINE_SOURCE == "pipeline" && $PIPELINE_ID == "Prod" && $ENV_ID == "GCP_Staging"
    - if: $CI_PIPELINE_SOURCE == "pipeline" && $PIPELINE_ID == "Hotfix" && $ENV_ID == "GCP_Staging"

.gcp-uat-variables:
  variables:
    GCP_PROJECT_ID: level-ruler-447003-u3
    URL_MAP_NAME: glikeyoke-ui-uat-alb
    BUCKET_NAME: glikeyoke-ui-uat-bucket
    BASE_URL: https://api.uat.gcp.glideyoke.com
    AUTH_SERVICE_URL: "https://auth.uat.gcp.glideyoke.com"
    IDENTITY_SERVICE_URL: "https://identity-stage.gcp.glideyoke.com"
    MASTER_DOMAIN: "ui-stage"
    MEDIA_URL: "https://media.uat.gcp.glideyoke.com"
    DEFAULT_TENANT_ID: "master"
    TRACKING_SERVICE_URL: "https://api.uat.gcp.glideyoke.com/tracking"
    DOMAIN_PATTERN: "(\\w.+)"
  rules:
    - if: $CI_COMMIT_BRANCH == "main" || $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - $PACKAGE_DIR/**/*
    - if: $CI_PIPELINE_SOURCE == "pipeline" && $PIPELINE_ID == "UAT" && $ENV_ID == "GCP_UAT"
    - if: $CI_PIPELINE_SOURCE == "pipeline" && $PIPELINE_ID == "Prod" && $ENV_ID == "GCP_UAT"
    - if: $CI_PIPELINE_SOURCE == "pipeline" && $PIPELINE_ID == "Hotfix" && $ENV_ID == "GCP_UAT"

.npm-package:
  stage: package
  image: node:19-alpine
  when: manual
  script:
    - cd $PACKAGE_DIR
    - npm run build
  artifacts:
    paths:
      - $PACKAGE_DIR/dist/

.aws-bucket-publish:
  stage: publish
  script:
    - cd $PACKAGE_DIR/dist/
    - aws s3 sync . s3://$BUCKET_NAME/admin/

.gcp-bucket-publish:
  stage: publish
  script:
    - gcloud auth activate-service-account --key-file=/tmp/key.json
    - cd $PACKAGE_DIR/dist/
    - gcloud storage cp -r . gs://$BUCKET_NAME/admin/

.aws-cache-invalidate:
  stage: invalidate
  when: manual
  script:
    - aws cloudfront create-invalidation --distribution-id $CF_DISTRIBUTIONS_ID --paths "/admin/*"

.gcp-cache-invalidate:
  stage: invalidate
  when: manual
  script:
    - gcloud auth activate-service-account --key-file=/tmp/key.json
    - gcloud compute url-maps invalidate-cdn-cache $URL_MAP_NAME --path "/*" --project $GCP_PROJECT_ID

npm-install:
  stage: install
  image: node:19-alpine
  script:
    - echo "@tripudiotech:registry=https://${CI_SERVER_HOST}/api/v4/packages/npm/">.npmrc
    - echo "//${CI_SERVER_HOST}/api/v4/packages/npm/:_authToken=${GL_DEPLOY_TOKEN}">>.npmrc
    - echo "//${CI_SERVER_HOST}/api/v4/projects/********/packages/npm/:_authToken=${GL_DEPLOY_TOKEN}">>.npmrc
    - echo "//${CI_SERVER_HOST}/api/v4/projects/********/packages/npm/:_authToken=${GL_DEPLOY_TOKEN}">>.npmrc
    - npm install
  cache:
    key: $CI_COMMIT_REF_SLUG
    paths:
      - node_modules/
  artifacts:
    paths:
      - node_modules/
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_PIPELINE_SOURCE == "pipeline" && $PIPELINE_ID == "UAT"
    - if: $CI_PIPELINE_SOURCE == "pipeline" && $PIPELINE_ID == "Prod"
    - if: $CI_PIPELINE_SOURCE == "pipeline" && $PIPELINE_ID == "Hotfix"

aws-stag-invalidation:
  extends:
    - .aws-stag-variables
    - .aws-cache-invalidate
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

gcp-stag-invalidation:
  extends:
    - .gcp-stag-variables
    - .gcp-cache-invalidate
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_PIPELINE_SOURCE == "pipeline" && $PIPELINE_ID == "UAT" && $ENV_ID == "GCP_Staging"
    - if: $CI_PIPELINE_SOURCE == "pipeline" && $PIPELINE_ID == "Prod" && $ENV_ID == "GCP_Staging"
    - if: $CI_PIPELINE_SOURCE == "pipeline" && $PIPELINE_ID == "Hotfix" && $ENV_ID == "GCP_Staging"

gcp-uat-invalidation:
  extends:
    - .gcp-uat-variables
    - .gcp-cache-invalidate
  before_script:
    - echo $GCP_STAG_SA_KEY | base64 -d > /tmp/key.json
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_PIPELINE_SOURCE == "pipeline" && $PIPELINE_ID == "UAT" && $ENV_ID == "GCP_UAT"
    - if: $CI_PIPELINE_SOURCE == "pipeline" && $PIPELINE_ID == "Prod" && $ENV_ID == "GCP_UAT"
    - if: $CI_PIPELINE_SOURCE == "pipeline" && $PIPELINE_ID == "Hotfix" && $ENV_ID == "GCP_UAT"

include:
  - packages/*/.gitlab-ci.yml