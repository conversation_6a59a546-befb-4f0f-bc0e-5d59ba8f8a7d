{"name": "root", "private": true, "devDependencies": {"@quintype/copyright-header": "^1.3.4", "husky": "^9.1.7", "lerna": "^6.4.1"}, "scripts": {"bootstrap": "lerna bootstrap --hoist", "start": "lerna run start --stream", "start:parallel": "lerna run start --parallel", "build": "lerna run build", "clean": "lerna run clean --parallel", "copyright-header": "copyright-header -f copyright-header-template.js -i \"packages/**/src/**/*\" -s"}, "husky": {"hooks": {"pre-commit": "lerna run --concurrency 1 --stream precommit --since HEAD --exclude-dependents && npm run copyright-header"}}, "workspaces": {"packages": ["packages/**"]}, "dependencies": {"serve": "^14.2.0"}}